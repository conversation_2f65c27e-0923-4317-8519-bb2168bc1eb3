package com.lznbys.base.util;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.text.TextUtils;
import android.widget.EditText;
import android.widget.LinearLayout;

import com.lznbys.base.R;


/**
 * 简单提示弹窗
 */
public class DialogUtils {

    private DialogUtils() {

    }

    /**
     * 仅提示内容
     * @param message 提示内容
     * @param context context
     */
    public static void dialogMessage(String message, Context context) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle("提示");
        builder.setMessage(message);
        builder.setNegativeButton("确认", (dialog, id) -> dialog.cancel());
        AlertDialog dialog = builder.create();
        dialog.show();
    }

    /**
     * 有确认按钮的弹窗
     * @param message 提示内容
     * @param context context
     * @param callback 确认按钮执行的回调方法
     */
    public static void dialogDeleteMessage(String message, Context context, SimpleCallback callback) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle("提示");
        builder.setMessage(message);
        builder.setNegativeButton("取消", (dialog, id) -> dialog.cancel());
        builder.setPositiveButton("确认", (dialog, id) -> callback.doSomething());
        AlertDialog dialog = builder.create();
        dialog.show();
    }

    /**
     * 试用期到期提醒
     * @param context context
     */
    public static void dialogMessage( Context context) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle("提示");
        builder.setMessage("试用期已到请联系添加微信13750880401！");
        builder.setNegativeButton("确认", (dialog, id) -> dialog.cancel());
        AlertDialog dialog = builder.create();
        dialog.show();
    }

    /**
     * 设置文件名称
     * @param context context
     * @param title 弹窗标题
     * @param warning 输入框Hint内容
     * @param callback 字符串回调函数
     */
    @SuppressLint("UseCompatLoadingForDrawables")
    public static void dialogInput(Context context, String title, String warning, StringCallback callback) {
        // 创建一个AlertDialog.Builder实例
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        // 设置对话框的标题
        builder.setTitle(title);
        // 创建一个EditText作为输入框
        final EditText input = new EditText(context);
        // 设置EditText的一些属性，例如背景
        input.setBackground(context.getDrawable(R.drawable.edit_border));
        int paddingInPixels = (int) (16 * context.getResources().getDisplayMetrics().density);
        input.setPadding(paddingInPixels,paddingInPixels/2,paddingInPixels,paddingInPixels/2);
        // 创建一个LinearLayout容器
        LinearLayout container = new LinearLayout(context);
        container.setOrientation(LinearLayout.VERTICAL);
        LinearLayout.LayoutParams inputLayoutParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        // 将dp单位转换为像素，这里假设想要设置的边距为20dp
        int marginInPixels = (int) (22 * context.getResources().getDisplayMetrics().density);
        int marginInPixelsTop = (int) (16 * context.getResources().getDisplayMetrics().density);
        inputLayoutParams.setMargins(marginInPixels, marginInPixelsTop, marginInPixels, 0);
        // 应用LayoutParams到EditText
        input.setLayoutParams(inputLayoutParams);
        // 将EditText添加到容器中
        container.addView(input);
        // 将容器设置为AlertDialog的内容视图
        builder.setView(container);
        // 设置对话框的“确定”按钮
        builder.setPositiveButton("确定", (dialog, which) -> {
            // 获取EditText的值
            String inputValue = input.getText().toString();
            if (TextUtils.isEmpty(inputValue)) {
                DialogUtils.dialogMessage(warning, context);
            } else {
                // 处理用户的输入
                callback.doSomething(inputValue);
            }
        });
        // 设置对话框的“取消”按钮
        builder.setNegativeButton("取消", (dialog, which) -> dialog.cancel());
        // 创建并显示AlertDialog
        builder.create().show();
    }
}
