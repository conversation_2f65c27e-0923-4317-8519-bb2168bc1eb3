package com.lznbys.mvvm.ui.login

import android.content.Context
import android.text.TextUtils
import android.view.View
import androidx.lifecycle.MutableLiveData
import com.lznbys.base.util.KmsUtils
import com.lznbys.base.util.PopWarningUtils
import com.lznbys.base.util.TimeUtils
import com.lznbys.mvvm.App
import com.lznbys.mvvm.base.frame.BaseViewModel
import com.lznbys.mvvm.config.AppConfig
import com.lznbys.mvvm.data.entity.Activation
import com.lznbys.mvvm.data.entity.Dictionary
import com.lznbys.mvvm.data.repository.DictionaryRepository

class ActivationViewModel : BaseViewModel() {
    val activation = MutableLiveData(Activation("", "", false))
    private val dictionaryRepository = DictionaryRepository()

    fun activation(view: View) {
        val currentActivation = activation.value
        if (currentActivation == null) {
            PopWarningUtils.messagePop(view.context, "提示", "设备码和激活码不能为空！")
        } else {
            when {
                TextUtils.isEmpty(currentActivation.activationCode) -> {
                    PopWarningUtils.messagePop(view.context, "提示", "激活码不能为空！")
                }

                TextUtils.isEmpty(currentActivation.deviceId) -> {
                    PopWarningUtils.messagePop(view.context, "提示", "设备码不能为空！")
                }

                else -> {
                    if (KmsUtils.checkKms(view.context, currentActivation.activationCode)) {
                        disposable.add(
                            dictionaryRepository.getDictionariesByType("激活状态", "default")
                                .subscribe({ dictionaries ->
                                    if (dictionaries.isEmpty()) {
                                        val insertDictionary = Dictionary(
                                            key = "",
                                            value = "已激活",
                                            type = "激活状态",
                                            description = "激活状态",
                                            sort = 0,
                                            domain = AppConfig.DOMAIN_DEFAULT,
                                            createTime = TimeUtils.getNowTime(),
                                            updateTime = TimeUtils.getNowTime(),
                                            createUser = "system",
                                            updateUser = "system"
                                        ).apply {
                                            search = toString()
                                        }
                                        disposable.add(
                                            dictionaryRepository.insert(insertDictionary)
                                                .subscribe({
                                                    PopWarningUtils.messageDoPop(
                                                        view.context,
                                                        "提示",
                                                        "激活成功！",
                                                        { App.getCurrentActivity().finish() },
                                                        true
                                                    )
                                                }, { insertError ->
                                                    PopWarningUtils.messagePop(
                                                        view.context,
                                                        "错误",
                                                        "激活状态保存失败，请重试！"
                                                    )
                                                })
                                        )
                                    } else {
                                        if (dictionaries[0].value == "已激活") {
                                            PopWarningUtils.messageDoPop(
                                                view.context,
                                                "提示",
                                                "设备已激活！",
                                                { App.getCurrentActivity().finish() },
                                                true
                                            )
                                        } else {
                                            dictionaries[0].value = "已激活"
                                            disposable.add(
                                                dictionaryRepository.update(dictionaries[0])
                                                    .subscribe({
                                                        PopWarningUtils.messageDoPop(
                                                            view.context,
                                                            "提示",
                                                            "激活成功！",
                                                            { App.getCurrentActivity().finish() },
                                                            true
                                                        )
                                                    }, { insertError ->
                                                        PopWarningUtils.messagePop(
                                                            view.context,
                                                            "错误",
                                                            "激活状态保存失败，请重试！"
                                                        )
                                                    })
                                            )
                                        }
                                    }
                                }, { throwable ->
                                    PopWarningUtils.messagePop(
                                        view.context,
                                        "错误",
                                        "激活状态保存失败，请重试！"
                                    )
                                })
                        )
                    } else {
                        PopWarningUtils.messagePop(
                            view.context,
                            "提示",
                            "激活码错误，请重新输入或联系13750880401！"
                        )
                    }
                }
            }
        }
    }

    fun getDeviceId(context: Context) {
        activation.value?.let { currentActivation ->
            val deviceId = KmsUtils.getUniqueID(context)
            currentActivation.deviceId = deviceId
            //  优博讯(张策拿来的) deviceId == "8bd2a47766404fd7"
            //  成为C5(北京客户) deviceId == "9ef9883f0ee86c56"
            //  成为C72(咸鱼：幸运的ROC) deviceId == "49846a2be0a1f435"
            if (deviceId == "9ef9883f0ee86c56" || deviceId == "435d2512205dd7ba" || deviceId == "49846a2be0a1f435" || deviceId == "03f5977f88860975" || deviceId == "6bff97748e1547a5" || deviceId == "7a7b5a06e904c67d") {
                currentActivation.activationCode = KmsUtils.createKmsKey(deviceId)
            } else {
                currentActivation.activationCode = ""
            }
        }
    }

} 