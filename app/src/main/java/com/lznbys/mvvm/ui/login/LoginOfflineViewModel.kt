package com.lznbys.mvvm.ui.login

import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.util.Log
import android.view.View
import androidx.databinding.ObservableBoolean
import androidx.lifecycle.MutableLiveData
import com.hjq.toast.Toaster
import com.lznbys.base.util.KmsUtils
import com.lznbys.mvvm.App.Companion.getCurrentActivity
import com.lznbys.mvvm.base.frame.BaseViewModel
import com.lznbys.mvvm.config.AppConfig
import com.lznbys.mvvm.config.UserOfflineConfig
import com.lznbys.mvvm.data.repository.DictionaryRepository
import com.lznbys.mvvm.data.repository.UserRepository
import com.lznbys.mvvm.ui.MainActivity

class LoginOfflineViewModel : BaseViewModel() {

    val tenantName = MutableLiveData("") // 租户名称
    val username = MutableLiveData("") // 用户名
    val password = MutableLiveData("") // 密码
    var autoLogin = MutableLiveData<Boolean>()
    var rememberMe = MutableLiveData<Boolean>()
    var versionName = MutableLiveData("v1.1")
    var enableTenant = UserOfflineConfig.enableTenant


    val visibility = ObservableBoolean(true)
    val activation = ObservableBoolean(false)
    private val userRepository = UserRepository()
    private val dictionaryRepository = DictionaryRepository()

    fun initData() {
        if (UserOfflineConfig.rememberMe) {
            tenantName.value = UserOfflineConfig.tenantName
            username.value = UserOfflineConfig.username
            password.value = UserOfflineConfig.password
            rememberMe.value = UserOfflineConfig.rememberMe
            autoLogin.value = UserOfflineConfig.isAutoLogin
            if (UserOfflineConfig.isAutoLogin) {
                autoLogin.postValue(true)
                login(null)
            }
        } else {
            UserOfflineConfig.password = ""
        }
    }

    fun visibility(view: View) {
        visibility.set(!visibility.get())
    }

    fun routerToActivation(view: View) {
        val intent = Intent(view.context, ActivationActivity::class.java)
        view.context.startActivity(intent)
    }

    fun routerToRegister(view: View) {
        val intent = Intent(view.context, RegisterActivity::class.java)
        view.context.startActivity(intent)
    }

    fun login(view: View?) {
        KmsUtils.checkTimeOut {
            if (it) {
                message.value = "试用已过期！请联系微信添加13750880401获取正式版！"
            } else {
                disposable.add(dictionaryRepository.getDictionariesByType("激活状态", AppConfig.DOMAIN_DEFAULT)
                    .subscribe { dictionaries ->
                        // TODO 这里和激活有关，如果为true需要设置过期时间，如果为false判断条件过期时间改为9999年
                        //if (true) {
                        if (dictionaries.isNotEmpty() && dictionaries[0].value == "已激活") {
                            activation.set(true)
                            if (TextUtils.isEmpty(username.value) || TextUtils.isEmpty(password.value)) {
                                message.value = "账号或密码不能为空！"
                            } else {
                                when {
                                    TextUtils.isEmpty(username.value) -> {
                                        message.value = "账号不能为空！"
                                    }
                                    TextUtils.isEmpty(password.value) -> {
                                        message.value = "密码不能为空！"
                                    }
                                    else -> {
                                        loading.postValue(true)
                                        disposable.add(
                                            userRepository.findByUsernameAndPassword(
                                                username.value ?: "", password.value ?: "", tenantName.value ?: UserOfflineConfig.tenantName
                                            ).subscribe({ local ->
                                                if (local != null) {
                                                    Toaster.show("登录成功！")
                                                    UserOfflineConfig.tenantName = tenantName.value?: ""
                                                    UserOfflineConfig.username = username.value?: ""
                                                    UserOfflineConfig.password = password.value?: ""
                                                    UserOfflineConfig.rememberMe = rememberMe.value?: false
                                                    UserOfflineConfig.isAutoLogin = autoLogin.value?: false
                                                    UserOfflineConfig.isLogin = true
                                                    loading.postValue(false)
                                                    val intent = Intent(getCurrentActivity(), MainActivity::class.java)
                                                    getCurrentActivity().startActivity(intent)
                                                    getCurrentActivity().finish()
                                                } else {
                                                    message.value = "账号或密码错误！"
                                                    loading.postValue(false)
                                                }
                                            }, { error ->
                                                message.value = "登录失败${error.message}！"
                                                loading.postValue(false)
                                            })
                                        )
                                    }
                                }
                            }
                        } else {
                            if (!activation.get()) {
                                message.value = "请先激活设备！"
                                activation.set(false)
                            }
                        }
                    })
            }
        }

    }

    fun getActivation(context: Context) {
        disposable.add(dictionaryRepository.getDictionariesByType("激活状态", AppConfig.DOMAIN_DEFAULT)
            .subscribe { dictionaries ->
                if (dictionaries.isNotEmpty() && dictionaries[0].value == "已激活") {
                    activation.set(true)
                }
            })
    }

    fun initVersion() {
        val packageManager = getCurrentActivity().packageManager
        val packageInfo =
            packageManager.getPackageInfo(getCurrentActivity().packageName, 0)
        versionName.value = "版本号：${packageInfo.versionName ?: "v1.0"}"
    }

    fun clearTenantName(view: View) {
        tenantName.value = ""
    }

    fun clearAccount(view: View) {
        username.value = ""
    }

    fun initKey() {
        // 当前设备激活码
        Log.e("当前设备码", KmsUtils.getUniqueID(getCurrentActivity()))
        Log.e("当前设备激活码", KmsUtils.createKmsKey(KmsUtils.getUniqueID(getCurrentActivity())))

        // 咸鱼客户(幸运的ROC)
        Log.e("幸运的ROC：设备码", "49846a2be0a1f435")
        Log.e("幸运的ROC：激活码", KmsUtils.createKmsKey("49846a2be0a1f435"))
        // 微信咸鱼(董旭 James 咸鱼 服装)
        Log.e("董旭：设备码", "28d9307be6f8a9a8")
        Log.e("董旭：激活码", KmsUtils.createKmsKey("28d9307be6f8a9a8"))
        // 微信客户(张毅)
        Log.e("张毅：设备码", "03f5977f88860975")
        Log.e("张毅：激活码", KmsUtils.createKmsKey("03f5977f88860975"))
        // 微信咸鱼(董旭 James 咸鱼 服装)
        Log.e("1842：设备码", "cf89723e0c5d8bfb")
        Log.e("1842：激活码", KmsUtils.createKmsKey("cf89723e0c5d8bfb"))
        // 微信咸鱼(yinhezb 咸鱼 盘点)
        Log.e("yinhezb：设备码", "b6d0ecc8590e7fa3")
        Log.e("yinhezb：激活码", KmsUtils.createKmsKey("b6d0ecc8590e7fa3"))
        // 咸鱼(半只土豆)
        Log.e("半只土豆：设备码", "9ba08b83ba470208")
        Log.e("半只土豆：激活码", KmsUtils.createKmsKey("9ba08b83ba470208"))
        // 微信（成为代理）
        Log.e("成为代理：设备码", "36837e635483f6e2")
        Log.e("成为代理：激活码", KmsUtils.createKmsKey("36837e635483f6e2"))
        // 张策（优博讯）
        Log.e("张策（优博讯）：设备码", "8bd2a47766404fd7")
        Log.e("张策（优博讯）：激活码", KmsUtils.createKmsKey("8bd2a47766404fd7"))

        // 杭州-淘宝客户(C72 设备一)
        Log.e("杭州-淘宝客户 设备一：设备码", "6bff97748e1547a5")
        Log.e("杭州-淘宝客户 设备一：激活码", KmsUtils.createKmsKey("A57867535D910E"))
        // 杭州-淘宝客户(C72 设备二)
        Log.e("杭州-淘宝客户 设备二：设备码", "7a7b5a06e904c67d")
        Log.e("杭州-淘宝客户 设备二：激活码", KmsUtils.createKmsKey("12915DA7941FEE"))
    }
}