<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.lznbys.mvvm.ui.MainViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        android:orientation="vertical">

        <!-- 自定义Toolbar -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="?actionBarSize">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/colorPrimary">

                <TextView
                    style="@style/toolbar_title_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/main_toolbar_title" />

            </RelativeLayout>

        </androidx.cardview.widget.CardView>


        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <!-- 周转框流转 -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="8dp"
                    android:elevation="8dp"
                    app:cardCornerRadius="8dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="8dp"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:padding="8dp">

                            <View
                                android:layout_width="6dp"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_vertical"
                                android:background="@color/colorPrimary" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:text="资产管理"
                                android:textSize="16sp" />

                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <androidx.cardview.widget.CardView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="8dp"
                                android:layout_weight="1"
                                android:elevation="8dp"
                                android:visibility="visible"
                                app:cardCornerRadius="8dp">

                                <com.lznbys.base.widget.ImageTextView
                                    style="@style/branch_icon_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableTop="@drawable/main_materials_stack"
                                    android:onClick="@{viewModel::routerToMaterialOffline}"
                                    android:text="资产列表"
                                    app:drawableTopHeight="@dimen/main_button_size"
                                    app:drawableTopWidth="@dimen/main_button_size" />

                            </androidx.cardview.widget.CardView>

                            <androidx.cardview.widget.CardView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="8dp"
                                android:layout_weight="1"
                                android:elevation="8dp"
                                app:cardCornerRadius="8dp">

                                <com.lznbys.base.widget.ImageTextView
                                    style="@style/branch_icon_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableTop="@drawable/icon_login_out"
                                    android:onClick="@{viewModel::routerToInventory}"
                                    android:text="资产盘点"
                                    app:drawableTopHeight="@dimen/main_button_size"
                                    app:drawableTopWidth="@dimen/main_button_size" />

                            </androidx.cardview.widget.CardView>

                            <androidx.cardview.widget.CardView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="8dp"
                                android:layout_weight="1"
                                android:elevation="8dp"
                                app:cardCornerRadius="8dp">

                                <com.lznbys.base.widget.ImageTextView
                                    style="@style/branch_icon_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableTop="@drawable/main_materials_scan"
                                    android:onClick="@{viewModel::routerToScan}"
                                    android:text="扫描查询"
                                    app:drawableTopHeight="@dimen/main_button_size"
                                    app:drawableTopWidth="@dimen/main_button_size" />

                            </androidx.cardview.widget.CardView>


                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <androidx.cardview.widget.CardView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="8dp"
                                android:layout_weight="1"
                                android:elevation="8dp"
                                app:cardCornerRadius="8dp">

                                <com.lznbys.base.widget.ImageTextView
                                    style="@style/branch_icon_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableTop="@drawable/main_materials_output"
                                    android:onClick="@{viewModel::routerToAcceptance}"
                                    android:text="资产领用"
                                    app:drawableTopHeight="@dimen/main_button_size"
                                    app:drawableTopWidth="@dimen/main_button_size" />

                            </androidx.cardview.widget.CardView>

                            <androidx.cardview.widget.CardView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="8dp"
                                android:layout_weight="1"
                                android:elevation="8dp"
                                app:cardCornerRadius="8dp">

                                <com.lznbys.base.widget.ImageTextView
                                    style="@style/branch_icon_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableTop="@drawable/main_materials_input"
                                    android:text="资产归还"
                                    android:onClick="@{viewModel::routerToReturns}"
                                    app:drawableTopHeight="@dimen/main_button_size"
                                    app:drawableTopWidth="@dimen/main_button_size" />

                            </androidx.cardview.widget.CardView>

                            <androidx.cardview.widget.CardView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="8dp"
                                android:layout_weight="1"
                                android:elevation="8dp"
                                app:cardCornerRadius="8dp">

                                <com.lznbys.base.widget.ImageTextView
                                    style="@style/branch_icon_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableTop="@drawable/main_setting"
                                    android:onClick="@{viewModel::routerToInformation}"
                                    android:text="@string/information_toolbar_title"
                                    app:drawableTopHeight="@dimen/main_button_size"
                                    app:drawableTopWidth="@dimen/main_button_size" />

                            </androidx.cardview.widget.CardView>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <androidx.cardview.widget.CardView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="8dp"
                                android:layout_weight="1"
                                android:elevation="8dp"
                                app:cardCornerRadius="8dp">

                                <com.lznbys.base.widget.ImageTextView
                                    style="@style/branch_icon_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableTop="@drawable/main_setting_3d"
                                    android:onClick="@{viewModel::routerToSettingOffline}"
                                    android:text="@string/main_system_setting"
                                    app:drawableTopHeight="@dimen/main_button_size"
                                    app:drawableTopWidth="@dimen/main_button_size" />

                            </androidx.cardview.widget.CardView>


                            <androidx.cardview.widget.CardView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="8dp"
                                android:layout_weight="1"
                                android:elevation="8dp"
                                android:visibility="invisible"
                                app:cardCornerRadius="8dp">

                                <com.lznbys.base.widget.ImageTextView
                                    style="@style/branch_icon_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableTop="@drawable/icon_import_excel"
                                    android:text="@string/main_data_import"
                                    app:drawableTopHeight="@dimen/main_button_size"
                                    app:drawableTopWidth="@dimen/main_button_size" />

                            </androidx.cardview.widget.CardView>

                            <androidx.cardview.widget.CardView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="8dp"
                                android:layout_weight="1"
                                android:elevation="8dp"
                                android:visibility="invisible"
                                app:cardCornerRadius="8dp">

                                <com.lznbys.base.widget.ImageTextView
                                    style="@style/branch_icon_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableTop="@drawable/icon_export_excel"
                                    android:text="@string/main_data_export"
                                    app:drawableTopHeight="@dimen/main_button_size"
                                    app:drawableTopWidth="@dimen/main_button_size" />

                            </androidx.cardview.widget.CardView>

                        </LinearLayout>

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- 周转框管理 -->
                <androidx.cardview.widget.CardView
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="8dp"
                    android:elevation="8dp"
                    app:cardCornerRadius="8dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="8dp"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:padding="8dp">

                            <View
                                android:layout_width="6dp"
                                android:layout_height="match_parent"
                                android:layout_gravity="center_vertical"
                                android:background="@color/colorPrimary" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:text="周转框管理"
                                android:textSize="16sp" />

                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <androidx.cardview.widget.CardView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="8dp"
                                android:layout_weight="1"
                                android:elevation="8dp"
                                android:visibility="visible"
                                app:cardCornerRadius="8dp">

                                <com.lznbys.base.widget.ImageTextView
                                    style="@style/branch_icon_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableTop="@drawable/icon_order"
                                    android:text="盘点任务"
                                    app:drawableTopHeight="@dimen/main_button_size"
                                    app:drawableTopWidth="@dimen/main_button_size" />

                            </androidx.cardview.widget.CardView>

                            <androidx.cardview.widget.CardView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="8dp"
                                android:layout_weight="1"
                                android:elevation="8dp"
                                app:cardCornerRadius="8dp">

                                <com.lznbys.base.widget.ImageTextView
                                    style="@style/branch_icon_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableTop="@drawable/main_materials_input"
                                    android:text="周转框查询"
                                    app:drawableTopHeight="@dimen/main_button_size"
                                    app:drawableTopWidth="@dimen/main_button_size" />

                            </androidx.cardview.widget.CardView>

                            <androidx.cardview.widget.CardView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_margin="8dp"
                                android:layout_weight="1"
                                android:elevation="8dp"
                                android:visibility="invisible"
                                app:cardCornerRadius="8dp">

                                <com.lznbys.base.widget.ImageTextView
                                    style="@style/branch_icon_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:drawableTop="@drawable/main_materials_output"
                                    android:text="托盘出库"
                                    app:drawableTopHeight="@dimen/main_button_size"
                                    app:drawableTopWidth="@dimen/main_button_size" />

                            </androidx.cardview.widget.CardView>

                        </LinearLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="8dp"
                    android:elevation="8dp"
                    app:cardCornerRadius="8dp">

                    <androidx.appcompat.widget.LinearLayoutCompat
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingHorizontal="8dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="16dp">

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:id="@+id/llXzz"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/main_statistics_margin_horizontal"
                            android:background="@drawable/btn_gray_background"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center_vertical"
                            android:paddingVertical="@dimen/main_statistics_padding_vertical">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:padding="4dp"
                                android:src="@drawable/icon_circle_empty_1" />

                            <androidx.appcompat.widget.LinearLayoutCompat
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:paddingStart="@dimen/main_statistics_padding_start">

                                <androidx.appcompat.widget.AppCompatTextView
                                    style="@style/main_statistics_title"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="闲置中"
                                    tools:text="闲置中" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/tvXzz"
                                    style="@style/main_statistics_value"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:layout_weight="1"
                                    android:text="57.14%"
                                    tools:text="57.14%" />

                            </androidx.appcompat.widget.LinearLayoutCompat>

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/xzzCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="3个"
                                tools:text="3个" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="16dp"
                                android:layout_height="32dp"
                                android:padding="4dp"
                                android:src="@drawable/icon_mine_arrow" />

                        </androidx.appcompat.widget.LinearLayoutCompat>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginHorizontal="@dimen/main_statistics_margin_horizontal"
                            android:background="@color/dividing_line_gray" />

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:id="@+id/llYll"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/main_statistics_margin_horizontal"
                            android:background="@drawable/btn_gray_background"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center_vertical"
                            android:paddingVertical="@dimen/main_statistics_padding_vertical">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:padding="4dp"
                                android:src="@drawable/icon_circle_empty_3" />

                            <androidx.appcompat.widget.LinearLayoutCompat
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:paddingStart="@dimen/main_statistics_padding_start">

                                <androidx.appcompat.widget.AppCompatTextView
                                    style="@style/main_statistics_title"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="已领用"
                                    tools:text="已领用" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/tvYly"
                                    style="@style/main_statistics_value"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="57.14%"
                                    tools:text="57.14%" />

                            </androidx.appcompat.widget.LinearLayoutCompat>

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/ylyCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="3个"
                                tools:text="3个" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="16dp"
                                android:layout_height="32dp"
                                android:padding="4dp"
                                android:src="@drawable/icon_mine_arrow" />

                        </androidx.appcompat.widget.LinearLayoutCompat>

                        <View
                            android:visibility="gone"
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginHorizontal="@dimen/main_statistics_margin_horizontal"
                            android:background="@color/dividing_line_gray" />

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:visibility="gone"
                            android:id="@+id/llKdq"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/main_statistics_margin_horizontal"
                            android:background="@drawable/btn_gray_background"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center_vertical"
                            android:paddingVertical="@dimen/main_statistics_padding_vertical">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:padding="4dp"
                                android:src="@drawable/icon_circle_empty_a" />

                            <androidx.appcompat.widget.LinearLayoutCompat
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:paddingStart="@dimen/main_statistics_padding_start">

                                <androidx.appcompat.widget.AppCompatTextView
                                    style="@style/main_statistics_title"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="快到期"
                                    tools:text="快到期" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/tvKdq"
                                    style="@style/main_statistics_value"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="57.14%"
                                    tools:text="57.14%" />

                            </androidx.appcompat.widget.LinearLayoutCompat>

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/kdqCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="3个"
                                tools:text="3个" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="16dp"
                                android:layout_height="32dp"
                                android:padding="4dp"
                                android:src="@drawable/icon_mine_arrow" />

                        </androidx.appcompat.widget.LinearLayoutCompat>

                        <View
                            android:visibility="gone"
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginHorizontal="@dimen/main_statistics_margin_horizontal"
                            android:background="@color/dividing_line_gray" />

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:visibility="gone"
                            android:id="@+id/llYdq"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/main_statistics_margin_horizontal"
                            android:background="@drawable/btn_gray_background"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center_vertical"
                            android:paddingVertical="@dimen/main_statistics_padding_vertical">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:padding="4dp"
                                android:src="@drawable/icon_circle_empty_8" />

                            <androidx.appcompat.widget.LinearLayoutCompat
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:paddingStart="@dimen/main_statistics_padding_start">

                                <androidx.appcompat.widget.AppCompatTextView
                                    style="@style/main_statistics_title"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="已到期"
                                    tools:text="已到期" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/tvYdq"
                                    style="@style/main_statistics_value"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="57.14%"
                                    tools:text="57.14%" />

                            </androidx.appcompat.widget.LinearLayoutCompat>

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/ydqCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="3个"
                                tools:text="3个" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="16dp"
                                android:layout_height="32dp"
                                android:padding="4dp"
                                android:src="@drawable/icon_mine_arrow" />

                        </androidx.appcompat.widget.LinearLayoutCompat>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginHorizontal="@dimen/main_statistics_margin_horizontal"
                            android:background="@color/dividing_line_gray" />

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:id="@+id/llYbf"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/main_statistics_margin_horizontal"
                            android:background="@drawable/btn_gray_background"
                            android:clickable="true"
                            android:focusable="true"
                            android:gravity="center_vertical"
                            android:paddingVertical="@dimen/main_statistics_padding_vertical">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:padding="4dp"
                                android:src="@drawable/icon_circle_empty_9" />

                            <androidx.appcompat.widget.LinearLayoutCompat
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:paddingStart="@dimen/main_statistics_padding_start">

                                <androidx.appcompat.widget.AppCompatTextView
                                    style="@style/main_statistics_title"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="已报废"
                                    tools:text="已报废" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/tvYbf"
                                    style="@style/main_statistics_value"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="57.14%"
                                    tools:text="57.14%" />

                            </androidx.appcompat.widget.LinearLayoutCompat>

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/ybfCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="3个"
                                tools:text="3个" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="16dp"
                                android:layout_height="32dp"
                                android:padding="4dp"
                                android:src="@drawable/icon_mine_arrow" />

                        </androidx.appcompat.widget.LinearLayoutCompat>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginHorizontal="@dimen/main_statistics_margin_horizontal"
                            android:background="@color/dividing_line_gray" />

                    </androidx.appcompat.widget.LinearLayoutCompat>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </ScrollView>

    </LinearLayout>

</layout>
