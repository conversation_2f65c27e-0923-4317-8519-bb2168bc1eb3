<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- 通过WiFi或移动基站的方式获取用户错略的经纬度信息，定位精度大概误差在30~1500米 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- 通过GPS芯片接收卫星的定位信息，定位精度达10米以内 -->
    <uses-permission android:name="android.permission.ACCESS_GPS" /> <!-- gps定位 -->


    <application
        android:name=".App"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Mvvm"
        tools:targetApi="31">
        <activity
            android:name=".ui.login.LoginActivity"
            android:exported="true"
            android:theme="@style/Theme.Mvvm">

        </activity>
        <activity
            android:name=".ui.login.LoginOfflineActivity"
            android:exported="true"
            android:theme="@style/Theme.Mvvm">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.MainActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
        <activity
            android:name=".ui.login.RegisterActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
        <activity
            android:name=".ui.login.ActivationActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
        <activity
            android:name=".ui.setting.SettingOfflineActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
        <activity
            android:name=".ui.material.MaterialActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
        <activity
            android:name=".ui.material.AddMaterialActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
        <activity
            android:name=".ui.inventory.InventoryActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
        <activity
            android:name=".ui.inventory.AddInventoryActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
        <activity
            android:name=".ui.inventory.InventoryDetailActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
        <activity
            android:name=".ui.inventory.MaterialRangeActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
        <activity android:name=".ui.scan.ScanActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm"/>
        <activity android:name=".ui.acceptance.AcceptanceActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm"/>
        <activity android:name=".ui.acceptance.AcceptanceDetailActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm"/>
        <activity android:name=".ui.acceptance.AddAcceptanceActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm"/>
        <activity android:name=".ui.acceptance.MaterialSelectActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm"/>
        <activity android:name=".ui.returns.ReturnsActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm"/>
        <activity android:name=".ui.returns.ReturnsDetailActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm"/>
        <activity android:name=".ui.returns.MaterialSelectReturnsActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm"/>
        <activity android:name=".ui.returns.AddReturnsActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm"/>
        <activity android:name=".ui.material.LifeCycleActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm"/>
        <activity
            android:name=".ui.information.InformationActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
        <activity
            android:name=".ui.information.DepartmentActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
        <activity
            android:name=".ui.information.AreaActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
        <activity
            android:name=".ui.information.PersonActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
        <activity
            android:name=".ui.information.TypeActivity"
            android:exported="false"
            android:theme="@style/Theme.Mvvm" />
    </application>

</manifest>